#!/usr/bin/env python3
"""
Script para classificação de sinais de compra e venda usando XGBoost
Baseado na média OHLC das ações diversificadas com sinais futuros
Usa features: média OHLC (10 dias passados), volume, spread e volatilidade
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler
import seaborn as sns
import pickle
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling
from config_loader import config, setup_environment

def carregar_acoes_diversificadas():
    """
    Carrega todas as ações do arquivo CSV de diversificação usando configuração
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths['acoes_diversificacao']
        df = pd.read_csv(csv_path)

        # Pegar todas as ações (excluindo linhas vazias)
        acoes = []
        for _, row in df.iterrows():
            if pd.notna(row['Ticker']) and row['Ticker'].strip():
                ticker = row['Ticker'] + '.SA'
                nome = row['Nome']
                acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def baixar_dados_acao(ticker, nome):
    """
    Baixa dados históricos de uma ação usando configuração
    """
    try:
        # Usar configuração para período de dados do XGBoost
        periodo = config.get('xgboost.data_period')
        print(f"     📊 Baixando dados de {ticker} ({nome}) - período: {periodo}")

        # Baixar dados históricos
        dados = yf.download(ticker, period=periodo, progress=False)

        if dados.empty:
            print(f"     ❌ Nenhum dado encontrado para {ticker}")
            return None

        # Corrigir MultiIndex se necessário
        if isinstance(dados.columns, pd.MultiIndex):
            dados.columns = dados.columns.droplevel(1)

        # Verificar se tem dados suficientes
        if len(dados) < 100:
            print(f"     ⚠️ Poucos dados para {ticker}: {len(dados)} dias")
            return None

        return dados

    except Exception as e:
        print(f"     ❌ Erro ao baixar dados de {ticker}: {e}")
        return None

def corrigir_valores_zero_ultimo_dia(dados):
    """
    Corrige valores zero no último dia substituindo pelo penúltimo dia
    """
    if len(dados) < 2:
        return dados

    # Colunas OHLC para verificar
    colunas_ohlc = ['Open', 'High', 'Low', 'Close']

    # Verificar e corrigir valores zero no último dia
    for coluna in colunas_ohlc:
        if coluna in dados.columns:
            # Obter o último valor como escalar
            ultimo_valor = dados[coluna].iloc[-1]
            if hasattr(ultimo_valor, 'item'):
                ultimo_valor = ultimo_valor.item()

            # Verificar se é zero ou NaN
            if pd.isna(ultimo_valor) or ultimo_valor == 0:
                # Substituir pelo valor do penúltimo dia
                penultimo_valor = dados[coluna].iloc[-2]
                if hasattr(penultimo_valor, 'item'):
                    penultimo_valor = penultimo_valor.item()

                dados.loc[dados.index[-1], coluna] = penultimo_valor
                print(f"     🔧 Corrigido valor zero em {coluna}: {ultimo_valor} → {penultimo_valor}")

    return dados

def calcular_features_e_sinais(dados):
    """
    Calcula features e sinais de compra/venda baseados na média OHLC usando configuração
    """
    # Corrigir valores zero no último dia
    dados = corrigir_valores_zero_ultimo_dia(dados)

    # Calcular média OHLC
    dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4

    # Usar configurações do XGBoost
    volatility_window = config.get('xgboost.features.volatility_window')
    spread_multiplier = config.get('xgboost.features.spread_multiplier')
    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')

    # Calcular volatilidade usando janela configurada
    returns = dados['Media_OHLC'].pct_change()
    dados['Volatilidade'] = returns.rolling(window=volatility_window).std() * 100

    # Calcular spread estimado usando multiplicador configurado
    dados['Spread'] = dados['Volatilidade'] * spread_multiplier

    # Preencher NaN
    dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())
    dados['Volatilidade'] = dados['Volatilidade'].fillna(dados['Volatilidade'].mean())

    # Criar sinais futuros usando horizonte configurado
    dados['Media_OHLC_Futura'] = dados['Media_OHLC'].shift(-signal_horizon)

    # Sinal de venda: média OHLC atual > média OHLC futura
    dados['Sinal_Venda'] = (dados['Media_OHLC'] > dados['Media_OHLC_Futura']).astype(int)

    # Sinal de compra: média OHLC atual < média OHLC futura
    dados['Sinal_Compra'] = (dados['Media_OHLC'] < dados['Media_OHLC_Futura']).astype(int)

    # Criar features de média OHLC passada usando configuração
    for i in range(1, ohlc_lags + 1):
        dados[f'Media_OHLC_Lag_{i}'] = dados['Media_OHLC'].shift(i)

    # Remover linhas com NaN (início e fim da série)
    dados = dados.dropna()

    return dados

def preparar_dataset(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            datasets.append(dados_copy)

    if not datasets:
        print("❌ Nenhum dataset válido encontrado")
        return None, None, None, None

    # Combinar todos os datasets
    dataset_completo = pd.concat(datasets, ignore_index=True)

    # Features: média OHLC passada (configurável), volume, spread, volatilidade
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    feature_cols = [f'Media_OHLC_Lag_{i}' for i in range(1, ohlc_lags + 1)] + ['Volume', 'Spread', 'Volatilidade']

    # Verificar se todas as colunas existem
    colunas_existentes = [col for col in feature_cols if col in dataset_completo.columns]
    if len(colunas_existentes) != len(feature_cols):
        print(f"⚠️ Algumas features não encontradas. Disponíveis: {len(colunas_existentes)}/{len(feature_cols)}")
        feature_cols = colunas_existentes

    X = dataset_completo[feature_cols]

    # Targets: sinais de compra e venda
    y_compra = dataset_completo['Sinal_Compra']
    y_venda = dataset_completo['Sinal_Venda']

    print(f"📊 Dataset preparado:")
    print(f"   • Total de amostras: {len(X)}")
    print(f"   • Features: {len(feature_cols)}")
    print(f"   • Colunas de features: {feature_cols}")
    print(f"   • Sinais de compra (1): {y_compra.sum()} ({y_compra.mean()*100:.1f}%)")
    print(f"   • Sinais de venda (1): {y_venda.sum()} ({y_venda.mean()*100:.1f}%)")

    return X, y_compra, y_venda, feature_cols

def treinar_classificadores(X, y_compra, y_venda, feature_cols):
    """
    Treina classificadores XGBoost para sinais de compra e venda usando configuração
    """
    # Usar configurações do XGBoost
    use_scaler = config.get('xgboost.use_standard_scaler')
    test_size = config.get('xgboost.test_size')
    model_params = config.get('xgboost.model_params')

    # Normalizar features se configurado
    if use_scaler:
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        X_scaled = pd.DataFrame(X_scaled, columns=feature_cols)
    else:
        scaler = None
        X_scaled = X

    resultados = {}

    # Treinar classificador para sinais de compra
    print("\n🚀 Treinando classificador para sinais de COMPRA...")
    X_train, X_test, y_train, y_test = train_test_split(X_scaled, y_compra, test_size=test_size,
                                                        random_state=model_params['random_state'],
                                                        stratify=y_compra)

    modelo_compra = xgb.XGBClassifier(**model_params)
    
    modelo_compra.fit(X_train, y_train)
    y_pred_compra = modelo_compra.predict(X_test)
    
    print(f"   • Acurácia: {accuracy_score(y_test, y_pred_compra):.3f}")
    print(f"   • Relatório de classificação:")
    print(classification_report(y_test, y_pred_compra, target_names=['Não Comprar', 'Comprar']))
    
    resultados['compra'] = {
        'modelo': modelo_compra,
        'scaler': scaler,
        'accuracy': accuracy_score(y_test, y_pred_compra),
        'y_test': y_test,
        'y_pred': y_pred_compra,
        'feature_importance': modelo_compra.feature_importances_
    }
    
    # Treinar classificador para sinais de venda
    print("\n🚀 Treinando classificador para sinais de VENDA...")
    X_train, X_test, y_train, y_test = train_test_split(X_scaled, y_venda, test_size=test_size,
                                                        random_state=model_params['random_state'],
                                                        stratify=y_venda)

    modelo_venda = xgb.XGBClassifier(**model_params)
    
    modelo_venda.fit(X_train, y_train)
    y_pred_venda = modelo_venda.predict(X_test)
    
    print(f"   • Acurácia: {accuracy_score(y_test, y_pred_venda):.3f}")
    print(f"   • Relatório de classificação:")
    print(classification_report(y_test, y_pred_venda, target_names=['Não Vender', 'Vender']))
    
    resultados['venda'] = {
        'modelo': modelo_venda,
        'scaler': scaler,
        'accuracy': accuracy_score(y_test, y_pred_venda),
        'y_test': y_test,
        'y_pred': y_pred_venda,
        'feature_importance': modelo_venda.feature_importances_
    }
    
    return resultados, feature_cols

def criar_graficos_estruturados(resultados, feature_cols, acoes_dados):
    """
    Cria gráficos organizados seguindo estrutura MM/Butterworth
    """
    print(f"\n📊 Criando gráficos estruturados...")

    # Criar diretórios
    figures_dir = 'results/figures/xgboost_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # 1. Gráfico principal dos resultados do modelo
    criar_grafico_principal_modelo(resultados, feature_cols, figures_dir)

    # 2. Gráficos individuais por ação (amostra)
    criar_graficos_individuais_acoes(acoes_dados, figures_dir)

    print(f"   ✅ Gráficos salvos em: {figures_dir}/")

def criar_grafico_principal_modelo(resultados, feature_cols, figures_dir):
    """
    Cria o gráfico principal com resultados do modelo
    """
    # Configurar estilo
    plt.style.use('default')

    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Classificadores XGBoost - Sinais de Trading', fontsize=16, fontweight='bold')

    # Matriz de confusão - Compra
    cm_compra = confusion_matrix(resultados['compra']['y_test'], resultados['compra']['y_pred'])
    sns.heatmap(cm_compra, annot=True, fmt='d', cmap='Blues', ax=axes[0,0])
    axes[0,0].set_title(f'Matriz de Confusão - Sinais de Compra\nAcurácia: {resultados["compra"]["accuracy"]:.3f}')
    axes[0,0].set_xlabel('Predito')
    axes[0,0].set_ylabel('Real')
    axes[0,0].set_xticklabels(['Não Comprar', 'Comprar'])
    axes[0,0].set_yticklabels(['Não Comprar', 'Comprar'])

    # Matriz de confusão - Venda
    cm_venda = confusion_matrix(resultados['venda']['y_test'], resultados['venda']['y_pred'])
    sns.heatmap(cm_venda, annot=True, fmt='d', cmap='Reds', ax=axes[0,1])
    axes[0,1].set_title(f'Matriz de Confusão - Sinais de Venda\nAcurácia: {resultados["venda"]["accuracy"]:.3f}')
    axes[0,1].set_xlabel('Predito')
    axes[0,1].set_ylabel('Real')
    axes[0,1].set_xticklabels(['Não Vender', 'Vender'])
    axes[0,1].set_yticklabels(['Não Vender', 'Vender'])

    # Feature importance - Compra
    importance_compra = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados['compra']['feature_importance']
    }).sort_values('importance', ascending=True).tail(10)

    axes[1,0].barh(importance_compra['feature'], importance_compra['importance'], color='skyblue')
    axes[1,0].set_title('Top 10 Features - Sinais de Compra')
    axes[1,0].set_xlabel('Importância')

    # Feature importance - Venda
    importance_venda = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados['venda']['feature_importance']
    }).sort_values('importance', ascending=True).tail(10)

    axes[1,1].barh(importance_venda['feature'], importance_venda['importance'], color='lightcoral')
    axes[1,1].set_title('Top 10 Features - Sinais de Venda')
    axes[1,1].set_xlabel('Importância')

    plt.tight_layout()

    # Salvar gráfico principal
    nome_arquivo = os.path.join(figures_dir, 'xgboost_modelo_resultados.png')
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

def criar_graficos_individuais_acoes(acoes_dados, figures_dir):
    """
    Cria gráficos individuais para algumas ações (amostra)
    """
    # Selecionar algumas ações para gráficos individuais
    acoes_amostra = list(acoes_dados.keys())[:5]  # Primeiras 5 ações

    for ticker in acoes_amostra:
        dados = acoes_dados[ticker]
        if dados is not None and len(dados) > 100:
            try:
                criar_grafico_individual_acao(ticker, dados, figures_dir)
            except Exception as e:
                print(f"     ⚠️ Erro ao criar gráfico para {ticker}: {e}")

def criar_grafico_individual_acao(ticker, dados, figures_dir):
    """
    Cria gráfico individual para uma ação
    """
    ticker_clean = ticker.replace('.SA', '')

    # Configurar figura
    fig, axes = plt.subplots(2, 1, figsize=(12, 10))
    fig.suptitle(f'Análise XGBoost - {ticker_clean}', fontsize=14, fontweight='bold')

    # Últimos 6 meses para visualização
    dados_recentes = dados.tail(180)

    # Gráfico 1: Preço e Sinais
    ax1 = axes[0]
    ax1.plot(dados_recentes.index, dados_recentes['Media_OHLC'], label='Média OHLC', color='blue', linewidth=1)

    # Marcar sinais de compra
    sinais_compra = dados_recentes[dados_recentes['Sinal_Compra'] == 1]
    if len(sinais_compra) > 0:
        ax1.scatter(sinais_compra.index, sinais_compra['Media_OHLC'],
                   color='green', marker='^', s=50, label='Sinal Compra', alpha=0.7)

    # Marcar sinais de venda
    sinais_venda = dados_recentes[dados_recentes['Sinal_Venda'] == 1]
    if len(sinais_venda) > 0:
        ax1.scatter(sinais_venda.index, sinais_venda['Media_OHLC'],
                   color='red', marker='v', s=50, label='Sinal Venda', alpha=0.7)

    ax1.set_title('Preço e Sinais de Trading')
    ax1.set_ylabel('Preço (R$)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Gráfico 2: Volume e Volatilidade
    ax2 = axes[1]
    ax2_twin = ax2.twinx()

    ax2.bar(dados_recentes.index, dados_recentes['Volume'], alpha=0.3, color='gray', label='Volume')
    ax2_twin.plot(dados_recentes.index, dados_recentes['Volatilidade'], color='orange', label='Volatilidade')

    ax2.set_title('Volume e Volatilidade')
    ax2.set_ylabel('Volume')
    ax2_twin.set_ylabel('Volatilidade (%)')
    ax2.legend(loc='upper left')
    ax2_twin.legend(loc='upper right')

    plt.tight_layout()

    # Salvar gráfico individual
    nome_arquivo = os.path.join(figures_dir, f'xgboost_{ticker_clean}.png')
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

def salvar_modelos_estruturado(resultados, feature_cols):
    """
    Salva os modelos treinados em estrutura organizada
    """
    print(f"\n💾 Salvando modelos...")

    # Criar diretório para modelos XGBoost
    modelo_dir = 'results/models/xgboost_analysis'
    os.makedirs(modelo_dir, exist_ok=True)

    # Salvar modelo de compra
    modelo_compra_path = os.path.join(modelo_dir, 'modelo_sinais_compra.pkl')
    with open(modelo_compra_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados['compra']['modelo'],
            'scaler': resultados['compra']['scaler'],
            'feature_cols': feature_cols,
            'accuracy': resultados['compra']['accuracy'],
            'config_usado': {
                'data_period': config.get('xgboost.data_period'),
                'signal_horizon': config.get('xgboost.signal_horizon'),
                'ohlc_lags': config.get('xgboost.features.ohlc_lags'),
                'model_params': config.get('xgboost.model_params')
            }
        }, f)

    # Salvar modelo de venda
    modelo_venda_path = os.path.join(modelo_dir, 'modelo_sinais_venda.pkl')
    with open(modelo_venda_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados['venda']['modelo'],
            'scaler': resultados['venda']['scaler'],
            'feature_cols': feature_cols,
            'accuracy': resultados['venda']['accuracy'],
            'config_usado': {
                'data_period': config.get('xgboost.data_period'),
                'signal_horizon': config.get('xgboost.signal_horizon'),
                'ohlc_lags': config.get('xgboost.features.ohlc_lags'),
                'model_params': config.get('xgboost.model_params')
            }
        }, f)

    # Salvar resumo detalhado dos resultados
    resumo_path = os.path.join(modelo_dir, 'resumo_treinamento.txt')
    with open(resumo_path, 'w') as f:
        f.write("RESUMO DO TREINAMENTO XGBOOST - SINAIS DE TRADING\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Data de treinamento: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("CONFIGURAÇÕES UTILIZADAS:\n")
        f.write(f"  • Período de dados: {config.get('xgboost.data_period')}\n")
        f.write(f"  • Horizonte de sinais: {config.get('xgboost.signal_horizon')} dias\n")
        f.write(f"  • Lags OHLC: {config.get('xgboost.features.ohlc_lags')}\n")
        f.write(f"  • Janela volatilidade: {config.get('xgboost.features.volatility_window')}\n")
        f.write(f"  • Multiplicador spread: {config.get('xgboost.features.spread_multiplier')}\n\n")

        f.write(f"FEATURES UTILIZADAS ({len(feature_cols)}):\n")
        for i, feature in enumerate(feature_cols, 1):
            f.write(f"  {i:2d}. {feature}\n")

        f.write(f"\nRESULTADOS DO MODELO:\n")
        f.write(f"  • Acurácia Sinais de Compra: {resultados['compra']['accuracy']:.3f}\n")
        f.write(f"  • Acurácia Sinais de Venda: {resultados['venda']['accuracy']:.3f}\n\n")

        f.write(f"DEFINIÇÃO DOS SINAIS:\n")
        signal_horizon = config.get('xgboost.signal_horizon')
        f.write(f"  • Sinal de Compra: Média OHLC atual < Média OHLC {signal_horizon} dias à frente\n")
        f.write(f"  • Sinal de Venda: Média OHLC atual > Média OHLC {signal_horizon} dias à frente\n\n")

        f.write(f"PARÂMETROS DO XGBOOST:\n")
        model_params = config.get('xgboost.model_params')
        for param, valor in model_params.items():
            f.write(f"  • {param}: {valor}\n")

    print(f"   ✅ Modelos salvos em: {modelo_dir}/")
    print(f"   📄 Resumo: {resumo_path}")

    return modelo_dir

def salvar_dados_csv_estruturado(acoes_dados):
    """
    Salva dados em estrutura organizada seguindo padrão MM/Butterworth
    """
    print(f"\n💾 Salvando dados em estrutura organizada...")

    # Criar diretórios
    csv_dir = 'results/csv/xgboost_analysis'
    individual_dir = os.path.join(csv_dir, 'individual_stocks')
    os.makedirs(csv_dir, exist_ok=True)
    os.makedirs(individual_dir, exist_ok=True)

    # Lista para dados completos
    dados_completos = []

    # Processar cada ação individualmente
    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            # Preparar dados para CSV
            dados_csv = dados.copy()
            ticker_clean = ticker.replace('.SA', '')
            dados_csv['Ticker'] = ticker_clean

            # Selecionar colunas relevantes
            colunas_csv = [
                'Ticker', 'Media_OHLC', 'Volume', 'Spread', 'Volatilidade',
                'Sinal_Compra', 'Sinal_Venda', 'Media_OHLC_Futura'
            ]

            # Verificar se todas as colunas existem
            colunas_existentes = [col for col in colunas_csv if col in dados_csv.columns]

            if len(colunas_existentes) >= 6:  # Pelo menos as colunas principais
                dados_selecionados = dados_csv[colunas_existentes].copy()
                dados_selecionados.reset_index(inplace=True)
                dados_selecionados['Data'] = dados_selecionados['Date'].dt.strftime('%Y-%m-%d')
                dados_selecionados.drop('Date', axis=1, inplace=True)

                # Reordenar colunas
                cols_ordenadas = ['Ticker', 'Data'] + [col for col in colunas_existentes if col != 'Ticker']
                dados_selecionados = dados_selecionados[cols_ordenadas]

                # Salvar arquivo individual
                arquivo_individual = os.path.join(individual_dir, f'xgboost_{ticker_clean}.csv')
                dados_selecionados.to_csv(arquivo_individual, index=False)

                dados_completos.append(dados_selecionados)

    if dados_completos:
        # Combinar todos os dados
        df_final = pd.concat(dados_completos, ignore_index=True)
        df_final = df_final.sort_values(['Ticker', 'Data'])

        # Salvar arquivo completo principal
        csv_completo_path = os.path.join(csv_dir, 'resultados_xgboost_completo.csv')
        df_final.to_csv(csv_completo_path, index=False)

        # Criar resumo por ação
        resumo_por_acao = df_final.groupby('Ticker').agg({
            'Sinal_Compra': 'sum',
            'Sinal_Venda': 'sum',
            'Media_OHLC': ['first', 'last', 'mean'],
            'Volume': 'mean',
            'Volatilidade': 'mean',
            'Data': 'count'
        }).round(2)

        # Flatten column names
        resumo_por_acao.columns = ['Sinais_Compra', 'Sinais_Venda', 'Preco_Inicial', 'Preco_Final', 'Preco_Medio', 'Volume_Medio', 'Volatilidade_Media', 'Total_Dias']
        resumo_por_acao['Performance_%'] = ((resumo_por_acao['Preco_Final'] / resumo_por_acao['Preco_Inicial']) - 1) * 100
        resumo_por_acao['Total_Sinais'] = resumo_por_acao['Sinais_Compra'] + resumo_por_acao['Sinais_Venda']

        # Salvar resumo
        resumo_path = os.path.join(csv_dir, 'resultados_xgboost.csv')
        resumo_por_acao.to_csv(resumo_path)

        print(f"   ✅ Dados salvos em: {csv_dir}/")
        print(f"   📊 Total de registros: {len(df_final):,}")
        print(f"   📈 Ações processadas: {df_final['Ticker'].nunique()}")
        print(f"   📁 Arquivos individuais: {individual_dir}/")

        # Estatísticas dos sinais
        total_compra = df_final['Sinal_Compra'].sum()
        total_venda = df_final['Sinal_Venda'].sum()
        total_registros = len(df_final)

        print(f"   🟢 Sinais de Compra: {total_compra:,} ({total_compra/total_registros*100:.1f}%)")
        print(f"   🔴 Sinais de Venda: {total_venda:,} ({total_venda/total_registros*100:.1f}%)")

        return csv_completo_path
    else:
        print(f"   ❌ Nenhum dado válido para salvar")
        return None

def carregar_acoes_carteira():
    """
    Carrega as ações da carteira do arquivo CSV usando configuração
    Filtra apenas ações com quantidade líquida > 0 (ainda na carteira)
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Pegar todas as ações da carteira e calcular quantidade líquida
        acoes = []
        tickers_processados = set()

        for _, row in df.iterrows():
            if pd.notna(row['ticker']) and row['ticker'].strip():
                ticker = row['ticker'].strip()

                # Evitar duplicatas
                if ticker in tickers_processados:
                    continue
                tickers_processados.add(ticker)

                # Calcular quantidade líquida (compras - vendas)
                quantidade_liquida = obter_quantidade_carteira(ticker)

                # Incluir apenas ações com quantidade > 0 (ainda na carteira)
                if quantidade_liquida > 0:
                    # Extrair nome da empresa do ticker (simplificado)
                    nome = ticker.replace('.SA', '')
                    acoes.append((ticker, nome, quantidade_liquida))

        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo da carteira: {e}")
        return []

def obter_quantidade_carteira(ticker):
    """
    Obtém a quantidade líquida de ações de um ticker na carteira usando configuração
    Valores negativos na quantidade indicam vendas
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Filtrar por ticker e somar quantidades (compras - vendas)
        ticker_data = df[df['ticker'] == ticker]
        if not ticker_data.empty:
            quantidade_liquida = ticker_data['quantidade'].sum()
            return quantidade_liquida
        else:
            return 0

    except Exception as e:
        print(f"❌ Erro ao obter quantidade da carteira para {ticker}: {e}")
        return 0

def imprimir_recomendacoes_ultimo_dia(acoes_dados):
    """
    Imprime recomendações de compra e venda para o último dia de dados
    Similar ao formato do análise Butterworth
    """
    print(f"\n📊 ESTRATÉGIA DE TRADING - CLASSIFICADOR XGBOOST")
    print(f"=" * 60)
    print(f"🎯 Sinais detectados para o último dia disponível:")

    # Carregar informações da carteira
    try:
        acoes_carteira = carregar_acoes_carteira()
        tickers_carteira = {ticker for ticker, _, _ in acoes_carteira}
        carteira_info = {ticker: (nome, qtd) for ticker, nome, qtd in acoes_carteira}
    except:
        tickers_carteira = set()
        carteira_info = {}

    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            # Pegar o último dia com dados válidos (não NaN)
            dados_validos = dados.dropna(subset=['Sinal_Compra', 'Sinal_Venda'])

            if len(dados_validos) > 0:
                ultimo_dia = dados_validos.iloc[-1]
                ticker_clean = ticker.replace('.SA', '')
                nome_empresa = ticker_clean  # Nome simplificado

                # Verificar sinais de compra
                if ultimo_dia['Sinal_Compra'] == 1:
                    sinais_compra.append({
                        'ticker': ticker,
                        'ticker_clean': ticker_clean,
                        'nome': nome_empresa,
                        'preco': ultimo_dia['Media_OHLC'],
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'na_carteira': ticker in tickers_carteira,
                        'quantidade_carteira': carteira_info.get(ticker, (None, 0))[1]
                    })

                # Verificar sinais de venda
                if ultimo_dia['Sinal_Venda'] == 1:
                    sinais_venda.append({
                        'ticker': ticker,
                        'ticker_clean': ticker_clean,
                        'nome': nome_empresa,
                        'preco': ultimo_dia['Media_OHLC'],
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'na_carteira': ticker in tickers_carteira,
                        'quantidade_carteira': carteira_info.get(ticker, (None, 0))[1]
                    })

    # Verificar se há sinais
    if not sinais_compra and not sinais_venda:
        print("✅ Nenhum sinal de compra ou venda detectado para o último dia.")
        return

    # Exibir sinais de compra
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        for sinal in sorted(sinais_compra, key=lambda x: x['ticker_clean']):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            print(f"   📈 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
            print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🎯 Sinal: XGBoost prevê alta no preço")
            if sinal['na_carteira']:
                print(f"      ✅ VOCÊ JÁ POSSUI: {sinal['quantidade_carteira']:.0f} ações")
            print()

    # Exibir sinais de venda
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações):")
        print("-" * 60)

        # Separar ações da carteira das demais
        vendas_carteira = [s for s in sinais_venda if s['na_carteira']]
        vendas_outras = [s for s in sinais_venda if not s['na_carteira']]

        if vendas_carteira:
            print("   🎯 AÇÕES DA SUA CARTEIRA:")
            for sinal in sorted(vendas_carteira, key=lambda x: x['ticker_clean']):
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
                print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]}) ⚠️ VOCÊ POSSUI")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      📊 Volume: {sinal['volume']:,.0f}")
                if sinal['volatilidade'] > 0:
                    print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
                if sinal['spread'] > 0:
                    print(f"      💹 Spread: {sinal['spread']:.4f}")
                print(f"      🎯 Sinal: XGBoost prevê queda no preço")
                print(f"      🏦 Quantidade na carteira: {sinal['quantidade_carteira']:.0f} ações")
                print()

        if vendas_outras:
            print("   📊 OUTRAS AÇÕES:")
            for sinal in sorted(vendas_outras, key=lambda x: x['ticker_clean']):
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
                print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      📊 Volume: {sinal['volume']:,.0f}")
                if sinal['volatilidade'] > 0:
                    print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
                if sinal['spread'] > 0:
                    print(f"      💹 Spread: {sinal['spread']:.4f}")
                print(f"      🎯 Sinal: XGBoost prevê queda no preço")
                print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🟢 Compra: {len(sinais_compra)} ações")
    print(f"   🔴 Venda: {len(sinais_venda)} ações")
    if sinais_venda:
        vendas_carteira = [s for s in sinais_venda if s['na_carteira']]
        if vendas_carteira:
            print(f"   ⚠️  Vendas na sua carteira: {len(vendas_carteira)} ações")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")

def main():
    """
    Função principal
    """
    # Configurar ambiente
    setup_environment()
    
    print("🤖 CLASSIFICADOR XGBOOST - SINAIS DE TRADING")
    print("=" * 80)
    print("📊 Baseado na média OHLC das ações diversificadas")

    # Mostrar configurações
    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    data_period = config.get('xgboost.data_period')

    print(f"🎯 Sinais: Compra/Venda baseados em {signal_horizon} dias à frente")
    print(f"🔧 Features: Média OHLC passada ({ohlc_lags} dias), Volume, Spread, Volatilidade")
    print(f"📅 Período de dados: {data_period}")
    print("=" * 80)
    
    # Carregar lista de ações
    acoes = carregar_acoes_diversificadas()
    if not acoes:
        print("❌ Nenhuma ação encontrada")
        return
    
    # Baixar dados e calcular features
    print(f"\n📥 Baixando dados de {len(acoes)} ações...")
    acoes_dados = {}
    
    for ticker, nome in acoes:
        dados = baixar_dados_acao(ticker, nome)
        if dados is not None:
            dados_processados = calcular_features_e_sinais(dados)
            acoes_dados[ticker] = dados_processados
    
    print(f"✅ Processadas {len(acoes_dados)} ações com sucesso")

    # Salvar sinais em CSV
    salvar_dados_csv_estruturado(acoes_dados)

    # Preparar dataset
    print(f"\n🔧 Preparando dataset...")
    X, y_compra, y_venda, feature_cols = preparar_dataset(acoes_dados)
    
    if X is None:
        print("❌ Erro ao preparar dataset")
        return
    
    # Treinar classificadores
    resultados, feature_cols = treinar_classificadores(X, y_compra, y_venda, feature_cols)
    
    # Criar gráficos
    print(f"\n📊 Criando gráficos...")
    criar_graficos_estruturados(resultados, feature_cols, acoes_dados)

    # Salvar modelos
    print(f"\n💾 Salvando modelos...")
    salvar_modelos_estruturado(resultados, feature_cols)

    # Imprimir recomendações para o último dia
    imprimir_recomendacoes_ultimo_dia(acoes_dados)

    print(f"\n✅ Análise concluída!")
    print(f"📊 Resultados:")
    print(f"   • Acurácia Sinais de Compra: {resultados['compra']['accuracy']:.3f}")
    print(f"   • Acurácia Sinais de Venda: {resultados['venda']['accuracy']:.3f}")

if __name__ == "__main__":
    main()
